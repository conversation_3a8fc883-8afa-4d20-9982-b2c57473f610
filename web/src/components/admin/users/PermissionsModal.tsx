'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Users, 
  MessageSquare, 
  Settings, 
  Zap, 
  Share, 
  Download,
  Upload,
  Mic,
  Image,
  Code,
  Search,
  FileText,
  Workspace,
  Database,
  Key
} from 'lucide-react';
import { useI18n } from '@/lib/i18n';
import { useAuthS<PERSON> } from '@/lib/stores';
import { toast } from 'sonner';

interface UserPermissions {
  // Workspace permissions
  workspace: {
    models: boolean;
    knowledge: boolean;
    prompts: boolean;
    tools: boolean;
  };
  
  // Sharing permissions  
  sharing: {
    enable_community_sharing: boolean;
  };
  
  // Chat permissions
  chat: {
    file_upload: boolean;
    system_prompt: boolean;
    delete: boolean;
    edit: boolean;
    share: boolean;
    export: boolean;
    voice: boolean;
    temporary: boolean;
  };
  
  // Feature permissions
  features: {
    direct_tool_server_access: boolean;
    web_search: boolean;
    image_generation: boolean;
    code_interpreter: boolean;
    notes: boolean;
  };
}

interface PermissionsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'user';
  } | null;
  onPermissionsUpdated?: (permissions: UserPermissions) => void;
}

const defaultPermissions: UserPermissions = {
  workspace: {
    models: true,
    knowledge: true,
    prompts: true,
    tools: true,
  },
  sharing: {
    enable_community_sharing: false,
  },
  chat: {
    file_upload: true,
    system_prompt: false,
    delete: true,
    edit: true,
    share: true,
    export: true,
    voice: true,
    temporary: false,
  },
  features: {
    direct_tool_server_access: false,
    web_search: true,
    image_generation: true,
    code_interpreter: true,
    notes: true,
  },
};

export const PermissionsModal: React.FC<PermissionsModalProps> = ({
  open,
  onOpenChange,
  user,
  onPermissionsUpdated
}) => {
  const { t } = useI18n();
  const { token } = useAuthStore();
  const [permissions, setPermissions] = useState<UserPermissions>(defaultPermissions);
  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [originalPermissions, setOriginalPermissions] = useState<UserPermissions>(defaultPermissions);

  // Load user permissions when modal opens
  useEffect(() => {
    if (open && user) {
      loadUserPermissions();
    }
  }, [open, user]);

  // Track changes
  useEffect(() => {
    setHasChanges(JSON.stringify(permissions) !== JSON.stringify(originalPermissions));
  }, [permissions, originalPermissions]);

  const loadUserPermissions = async () => {
    if (!user || !token) return;

    setLoading(true);
    try {
      // TODO: Implement API call to get user permissions
      const response = await fetch(`/api/admin/users/${user.id}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load user permissions');
      }

      const userPermissions = await response.json();
      setPermissions(userPermissions);
      setOriginalPermissions(userPermissions);
    } catch (error) {
      console.error('Error loading user permissions:', error);
      // Use default permissions for demo
      setPermissions(defaultPermissions);
      setOriginalPermissions(defaultPermissions);
    } finally {
      setLoading(false);
    }
  };

  const handleSavePermissions = async () => {
    if (!user || !token) return;

    setLoading(true);
    try {
      // TODO: Implement API call to update user permissions
      const response = await fetch(`/api/admin/users/${user.id}/permissions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(permissions),
      });

      if (!response.ok) {
        throw new Error('Failed to update user permissions');
      }

      toast.success('Permissions updated successfully');
      setOriginalPermissions(permissions);
      
      if (onPermissionsUpdated) {
        onPermissionsUpdated(permissions);
      }
      
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating user permissions:', error);
      toast.error('Failed to update permissions');
    } finally {
      setLoading(false);
    }
  };

  const updatePermission = (category: keyof UserPermissions, key: string, value: boolean) => {
    setPermissions(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const resetToDefaults = () => {
    setPermissions(defaultPermissions);
  };

  const PermissionItem: React.FC<{
    icon: React.ReactNode;
    title: string;
    description: string;
    checked: boolean;
    onChange: (checked: boolean) => void;
    disabled?: boolean;
  }> = ({ icon, title, description, checked, onChange, disabled = false }) => (
    <div className="flex items-center justify-between py-3">
      <div className="flex items-start gap-3 flex-1">
        <div className="mt-1">{icon}</div>
        <div className="flex-1">
          <div className="font-medium text-sm">{title}</div>
          <div className="text-xs text-gray-500 mt-1">{description}</div>
        </div>
      </div>
      <Switch
        checked={checked}
        onCheckedChange={onChange}
        disabled={disabled}
      />
    </div>
  );

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] h-[700px] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Manage Permissions
          </DialogTitle>
          <DialogDescription>
            Configure detailed permissions for {user.name} ({user.email})
          </DialogDescription>
          {user.role === 'admin' && (
            <Badge variant="destructive" className="w-fit">
              Admin users have all permissions by default
            </Badge>
          )}
        </DialogHeader>

        <Tabs defaultValue="workspace" className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="workspace">Workspace</TabsTrigger>
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="sharing">Sharing</TabsTrigger>
          </TabsList>

          <ScrollArea className="flex-1 mt-4">
            <TabsContent value="workspace" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Workspace className="w-4 h-4" />
                    Workspace Access
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <PermissionItem
                    icon={<Database className="w-4 h-4" />}
                    title="Models"
                    description="Access to view and use AI models"
                    checked={permissions.workspace.models}
                    onChange={(checked) => updatePermission('workspace', 'models', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<FileText className="w-4 h-4" />}
                    title="Knowledge Base"
                    description="Access to knowledge base and documents"
                    checked={permissions.workspace.knowledge}
                    onChange={(checked) => updatePermission('workspace', 'knowledge', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<MessageSquare className="w-4 h-4" />}
                    title="Prompts"
                    description="Access to prompt templates and management"
                    checked={permissions.workspace.prompts}
                    onChange={(checked) => updatePermission('workspace', 'prompts', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<Zap className="w-4 h-4" />}
                    title="Tools"
                    description="Access to external tools and integrations"
                    checked={permissions.workspace.tools}
                    onChange={(checked) => updatePermission('workspace', 'tools', checked)}
                    disabled={user.role === 'admin'}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="chat" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <MessageSquare className="w-4 h-4" />
                    Chat Permissions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <PermissionItem
                    icon={<Upload className="w-4 h-4" />}
                    title="File Upload"
                    description="Upload files and attachments to chats"
                    checked={permissions.chat.file_upload}
                    onChange={(checked) => updatePermission('chat', 'file_upload', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<Settings className="w-4 h-4" />}
                    title="System Prompt"
                    description="Modify system prompts and behavior"
                    checked={permissions.chat.system_prompt}
                    onChange={(checked) => updatePermission('chat', 'system_prompt', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<Share className="w-4 h-4" />}
                    title="Share Chats"
                    description="Share conversations with others"
                    checked={permissions.chat.share}
                    onChange={(checked) => updatePermission('chat', 'share', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<Download className="w-4 h-4" />}
                    title="Export Chats"
                    description="Export conversation history"
                    checked={permissions.chat.export}
                    onChange={(checked) => updatePermission('chat', 'export', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<Mic className="w-4 h-4" />}
                    title="Voice Input"
                    description="Use voice input and speech-to-text"
                    checked={permissions.chat.voice}
                    onChange={(checked) => updatePermission('chat', 'voice', checked)}
                    disabled={user.role === 'admin'}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="features" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Zap className="w-4 h-4" />
                    Feature Access
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <PermissionItem
                    icon={<Key className="w-4 h-4" />}
                    title="Direct Tool Server Access"
                    description="Direct access to tool servers and APIs"
                    checked={permissions.features.direct_tool_server_access}
                    onChange={(checked) => updatePermission('features', 'direct_tool_server_access', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<Search className="w-4 h-4" />}
                    title="Web Search"
                    description="Search the web and access external content"
                    checked={permissions.features.web_search}
                    onChange={(checked) => updatePermission('features', 'web_search', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<Image className="w-4 h-4" />}
                    title="Image Generation"
                    description="Generate images using AI models"
                    checked={permissions.features.image_generation}
                    onChange={(checked) => updatePermission('features', 'image_generation', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<Code className="w-4 h-4" />}
                    title="Code Interpreter"
                    description="Execute code and use code interpreter"
                    checked={permissions.features.code_interpreter}
                    onChange={(checked) => updatePermission('features', 'code_interpreter', checked)}
                    disabled={user.role === 'admin'}
                  />
                  <Separator />
                  <PermissionItem
                    icon={<FileText className="w-4 h-4" />}
                    title="Notes"
                    description="Create and manage notes"
                    checked={permissions.features.notes}
                    onChange={(checked) => updatePermission('features', 'notes', checked)}
                    disabled={user.role === 'admin'}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="sharing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Share className="w-4 h-4" />
                    Sharing Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <PermissionItem
                    icon={<Users className="w-4 h-4" />}
                    title="Community Sharing"
                    description="Share content with the Open WebUI community"
                    checked={permissions.sharing.enable_community_sharing}
                    onChange={(checked) => updatePermission('sharing', 'enable_community_sharing', checked)}
                    disabled={user.role === 'admin'}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </ScrollArea>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={resetToDefaults}>
            Reset to Defaults
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t('Cancel')}
            </Button>
            <Button 
              onClick={handleSavePermissions} 
              disabled={loading || !hasChanges}
            >
              {loading ? 'Saving...' : t('Save')}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};