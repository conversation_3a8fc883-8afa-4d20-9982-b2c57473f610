'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, MessageSquare, Calendar, Clock, Download, Trash2, Eye } from 'lucide-react';
import { useI18n } from '@/lib/i18n';
import { useAuthStore } from '@/lib/stores';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface Chat {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count: number;
  model?: string;
  tags?: string[];
  archived?: boolean;
  shared?: boolean;
}

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  model?: string;
}

interface UserChatsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: {
    id: string;
    name: string;
    email: string;
  } | null;
}

export const UserChatsModal: React.FC<UserChatsModalProps> = ({
  open,
  onOpenChange,
  user
}) => {
  const { t } = useI18n();
  const { token } = useAuthStore();
  const [chats, setChats] = useState<Chat[]>([]);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [messagesLoading, setMessagesLoading] = useState(false);

  // Load user chats when modal opens
  useEffect(() => {
    if (open && user) {
      loadUserChats();
    }
  }, [open, user]);

  const loadUserChats = async () => {
    if (!user || !token) return;

    setLoading(true);
    try {
      // TODO: Implement API call to get user chats
      const response = await fetch(`/api/admin/users/${user.id}/chats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load user chats');
      }

      const userChats = await response.json();
      setChats(userChats);
    } catch (error) {
      console.error('Error loading user chats:', error);
      toast.error('Failed to load user chats');
      
      // Mock data for demo
      setChats([
        {
          id: '1',
          title: 'Python Programming Help',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T11:45:00Z',
          message_count: 12,
          model: 'gpt-4',
          tags: ['programming', 'python'],
          archived: false,
          shared: false
        },
        {
          id: '2',
          title: 'Recipe Suggestions',
          created_at: '2024-01-14T15:20:00Z',
          updated_at: '2024-01-14T15:35:00Z',
          message_count: 8,
          model: 'gpt-3.5-turbo',
          tags: ['cooking', 'recipes'],
          archived: false,
          shared: true
        },
        {
          id: '3',
          title: 'Data Analysis Project',
          created_at: '2024-01-13T09:15:00Z',
          updated_at: '2024-01-13T16:30:00Z',
          message_count: 25,
          model: 'claude-3-opus',
          tags: ['data', 'analysis'],
          archived: true,
          shared: false
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const loadChatMessages = async (chat: Chat) => {
    if (!token) return;

    setMessagesLoading(true);
    try {
      // TODO: Implement API call to get chat messages
      const response = await fetch(`/api/admin/chats/${chat.id}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load chat messages');
      }

      const chatMessages = await response.json();
      setMessages(chatMessages);
    } catch (error) {
      console.error('Error loading chat messages:', error);
      toast.error('Failed to load chat messages');
      
      // Mock data for demo
      setMessages([
        {
          id: '1',
          role: 'user',
          content: 'Can you help me with Python programming?',
          timestamp: '2024-01-15T10:30:00Z',
        },
        {
          id: '2',
          role: 'assistant',
          content: 'Of course! I\'d be happy to help you with Python programming. What specific topic or problem would you like assistance with?',
          timestamp: '2024-01-15T10:30:30Z',
          model: chat.model
        },
        {
          id: '3',
          role: 'user',
          content: 'I need to create a function that sorts a list of dictionaries by a specific key.',
          timestamp: '2024-01-15T10:31:00Z',
        },
        {
          id: '4',
          role: 'assistant',
          content: 'Here\'s how you can sort a list of dictionaries by a specific key:\n\n```python\n# Sample data\ndata = [\n    {\'name\': \'Alice\', \'age\': 30},\n    {\'name\': \'Bob\', \'age\': 25},\n    {\'name\': \'Charlie\', \'age\': 35}\n]\n\n# Sort by age\nsorted_data = sorted(data, key=lambda x: x[\'age\'])\nprint(sorted_data)\n```\n\nThis will sort the list in ascending order by the \'age\' key.',
          timestamp: '2024-01-15T10:31:30Z',
          model: chat.model
        }
      ]);
    } finally {
      setMessagesLoading(false);
    }
  };

  const handleChatSelect = (chat: Chat) => {
    setSelectedChat(chat);
    loadChatMessages(chat);
  };

  const handleDeleteChat = async (chatId: string) => {
    if (!token) return;

    try {
      // TODO: Implement API call to delete chat
      const response = await fetch(`/api/admin/chats/${chatId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete chat');
      }

      setChats(chats.filter(c => c.id !== chatId));
      if (selectedChat?.id === chatId) {
        setSelectedChat(null);
        setMessages([]);
      }
      toast.success('Chat deleted successfully');
    } catch (error) {
      console.error('Error deleting chat:', error);
      toast.error('Failed to delete chat');
    }
  };

  const handleExportChat = async (chatId: string) => {
    if (!token) return;

    try {
      // TODO: Implement API call to export chat
      const response = await fetch(`/api/admin/chats/${chatId}/export`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export chat');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chat-${chatId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      toast.success('Chat exported successfully');
    } catch (error) {
      console.error('Error exporting chat:', error);
      toast.error('Failed to export chat');
    }
  };

  const filteredChats = chats.filter(chat =>
    chat.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chat.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] h-[600px] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            {user.name}'s Chats
          </DialogTitle>
          <DialogDescription>
            View and manage all conversations for {user.email}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex gap-4 min-h-0">
          {/* Chat List */}
          <div className="w-1/3 flex flex-col">
            <div className="mb-4">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search chats..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <ScrollArea className="flex-1">
              {loading ? (
                <div className="text-center p-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">Loading chats...</p>
                </div>
              ) : filteredChats.length > 0 ? (
                <div className="space-y-2">
                  {filteredChats.map((chat) => (
                    <Card
                      key={chat.id}
                      className={cn(
                        "cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",
                        selectedChat?.id === chat.id && "ring-2 ring-blue-500"
                      )}
                      onClick={() => handleChatSelect(chat)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-sm line-clamp-2">{chat.title}</h4>
                          <div className="flex gap-1 ml-2 flex-shrink-0">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleExportChat(chat.id);
                              }}
                              className="h-6 w-6 p-0"
                            >
                              <Download className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteChat(chat.id);
                              }}
                              className="h-6 w-6 p-0 text-red-600"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 text-xs text-gray-500 mb-2">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(chat.updated_at)}</span>
                          <MessageSquare className="w-3 h-3 ml-2" />
                          <span>{chat.message_count} messages</span>
                        </div>

                        <div className="flex items-center gap-1 mb-2">
                          {chat.model && (
                            <Badge variant="outline" className="text-xs">
                              {chat.model}
                            </Badge>
                          )}
                          {chat.archived && (
                            <Badge variant="secondary" className="text-xs">
                              Archived
                            </Badge>
                          )}
                          {chat.shared && (
                            <Badge variant="default" className="text-xs">
                              Shared
                            </Badge>
                          )}
                        </div>

                        {chat.tags && chat.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {chat.tags.map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4">
                  <MessageSquare className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No chats found</p>
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 flex flex-col">
            {selectedChat ? (
              <>
                <div className="border-l pl-4 mb-4">
                  <h3 className="font-semibold">{selectedChat.title}</h3>
                  <p className="text-sm text-gray-500">
                    {selectedChat.message_count} messages • {formatDate(selectedChat.updated_at)}
                  </p>
                </div>

                <ScrollArea className="flex-1 border rounded-lg p-4">
                  {messagesLoading ? (
                    <div className="text-center p-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="text-sm text-gray-500 mt-2">Loading messages...</p>
                    </div>
                  ) : messages.length > 0 ? (
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={cn(
                            "flex gap-3",
                            message.role === 'user' ? 'justify-end' : 'justify-start'
                          )}
                        >
                          <div
                            className={cn(
                              "max-w-[80%] rounded-lg p-3",
                              message.role === 'user'
                                ? 'bg-blue-500 text-white'
                                : message.role === 'assistant'
                                ? 'bg-gray-100 dark:bg-gray-800'
                                : 'bg-yellow-100 dark:bg-yellow-900'
                            )}
                          >
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-xs font-medium">
                                {message.role === 'user' ? 'User' : message.role === 'assistant' ? 'Assistant' : 'System'}
                              </span>
                              {message.model && (
                                <Badge variant="outline" className="text-xs">
                                  {message.model}
                                </Badge>
                              )}
                            </div>
                            <pre className="whitespace-pre-wrap text-sm font-sans">
                              {message.content}
                            </pre>
                            <div className="text-xs opacity-70 mt-2">
                              {formatDate(message.timestamp)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center p-4">
                      <MessageSquare className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">No messages found</p>
                    </div>
                  )}
                </ScrollArea>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center border-l pl-4">
                <div className="text-center">
                  <Eye className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Select a chat to view messages</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};